# منشئ نماذج PDF قابلة للتعبئة

هذا المشروع يحتوي على أدوات لتحويل placeholders في ملفات PDF إلى حقول نموذج قابلة للتعبئة.

## الملفات المتوفرة

### 1. `i.py` - النسخة الأساسية المحسنة
- تحويل placeholders إلى حقول نموذج فعلية
- دعم متعدد الصفحات
- إعدادات تنسيق قابلة للتخصيص
- إضافة تسميات للحقول

### 2. `enhanced_pdf_form_creator.py` - النسخة المتقدمة
- فئة شاملة مع خيارات متقدمة
- تخصيص كامل لمظهر الحقول
- مواضع مختلفة للتسميات
- معالجة أخطاء محسنة
- حفظ معلومات الحقول

### 3. `simple_pdf_form.py` - النسخة المبسطة
- دالة واحدة سهلة الاستخدام
- أمثلة متنوعة للاستخدام
- إعدادات مسبقة للأنماط المختلفة

## المتطلبات

```bash
pip install PyMuPDF
```

## كيفية الاستخدام

### الاستخدام الأساسي

```python
from simple_pdf_form import create_fillable_pdf_form

# قائمة الـ placeholders
placeholders = ["name", "date", "signature", "amount"]

# إنشاء النموذج
result = create_fillable_pdf_form(
    input_pdf="template.pdf",
    output_pdf="fillable_form.pdf",
    placeholders=placeholders
)

if result['success']:
    print(f"تم إنشاء {result['fields_created']} حقل")
```

### الاستخدام المتقدم

```python
from enhanced_pdf_form_creator import PDFFormCreator

# إنشاء منشئ النماذج
form_creator = PDFFormCreator("template.pdf", "advanced_form.pdf")

# تخصيص المظهر
form_creator.set_field_style(
    font_size=12,
    border_color=(0, 0.5, 0),      # أخضر
    fill_color=(0.95, 1, 0.95),    # خلفية خضراء فاتحة
    border_width=1.5
)

# إنشاء النموذج
result = form_creator.create_form(
    placeholders=placeholders,
    add_labels=True,
    label_position='top'
)
```

## تنسيق Placeholders

الكود يبحث عن placeholders بالتنسيق التالي:
```
{{placeholder_name}}
```

مثال:
- `{{name}}` سيصبح حقل نص باسم "name"
- `{{date}}` سيصبح حقل نص باسم "date"
- `{{signature}}` سيصبح حقل نص باسم "signature"

## خيارات التخصيص

### ألوان الحقول
```python
field_style = {
    'border_color': (0, 0, 1),      # أزرق (R, G, B)
    'fill_color': (0.95, 0.95, 1),  # خلفية زرقاء فاتحة
    'text_color': (0, 0, 0)         # نص أسود
}
```

### أحجام الحقول
```python
field_style = {
    'font_size': 12,
    'min_width': 100,
    'min_height': 20,
    'border_width': 1.5
}
```

### مواضع التسميات
- `'top'` - أعلى الحقل
- `'bottom'` - أسفل الحقل  
- `'left'` - يسار الحقل
- `'right'` - يمين الحقل

## أمثلة جاهزة

### نموذج مهني
```python
professional_style = {
    'font_size': 12,
    'border_color': (0.2, 0.2, 0.2),    # رمادي داكن
    'fill_color': (0.98, 0.98, 0.98),   # رمادي فاتح
    'border_width': 0.8
}
```

### نموذج ملون
```python
colorful_style = {
    'font_size': 11,
    'border_color': (1, 0.5, 0),        # برتقالي
    'fill_color': (1, 0.95, 0.8),       # خلفية برتقالية فاتحة
    'border_width': 2
}
```

## الميزات

✅ **حقول قابلة للتعبئة**: إنشاء حقول نموذج فعلية في PDF  
✅ **دعم متعدد الصفحات**: معالجة جميع صفحات الملف  
✅ **تخصيص كامل**: ألوان، خطوط، أحجام قابلة للتعديل  
✅ **تسميات الحقول**: إضافة تسميات في مواضع مختلفة  
✅ **سهولة الاستخدام**: واجهات برمجية بسيطة ومتقدمة  
✅ **معالجة الأخطاء**: رسائل خطأ واضحة ومفيدة  

## ملاحظات مهمة

1. **تأكد من وجود ملف PDF الأصلي** قبل تشغيل الكود
2. **استخدم تنسيق placeholders الصحيح**: `{{name}}`
3. **الحقول المنشأة قابلة للتعبئة** في أي قارئ PDF
4. **يمكن حفظ البيانات المدخلة** في معظم قارئات PDF
5. **الكود يحافظ على تنسيق الملف الأصلي** ويضيف الحقول فقط

## استكشاف الأخطاء

### خطأ: "لم يتم العثور على placeholders"
- تأكد من أن placeholders موجودة في الملف
- تحقق من تنسيق placeholders: `{{name}}`
- تأكد من أن أسماء placeholders صحيحة

### خطأ: "لا يمكن فتح الملف"
- تأكد من وجود ملف PDF في المسار المحدد
- تحقق من صحة اسم الملف والمسار
- تأكد من أن الملف غير مفتوح في برنامج آخر

### خطأ: "لا يمكن حفظ الملف"
- تأكد من وجود صلاحيات الكتابة في المجلد
- تحقق من أن اسم الملف الناتج صحيح
- تأكد من عدم فتح الملف الناتج في برنامج آخر

## المساهمة

يمكنك تحسين الكود أو إضافة ميزات جديدة مثل:
- دعم أنواع حقول أخرى (checkbox, dropdown)
- تصدير معلومات الحقول إلى JSON
- واجهة مستخدم رسومية
- دعم قوالب متعددة
