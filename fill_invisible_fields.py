import fitz  # PyMuPDF

def fill_pdf_fields(input_pdf, output_pdf, field_data):
    """
    ملء الحقول المخفية في PDF برمجياً

    Args:
        input_pdf: مسار ملف PDF مع الحقول المخفية
        output_pdf: مسار ملف PDF الناتج مع البيانات المملوءة
        field_data: قاموس يحتوي على أسماء الحقول والقيم المراد ملؤها
                   مثال: {"name": "أحمد محمد", "date": "2024-01-15"}

    Returns:
        dict: معلومات عن نتيجة العملية
    """
    
    try:
        # فتح ملف PDF
        doc = fitz.open(input_pdf)
        filled_fields = 0
        total_fields = 0
        
        # معالجة كل صفحة
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # الحصول على جميع الحقول في الصفحة
            widgets = page.widgets()
            
            for widget in widgets:
                total_fields += 1
                field_name = widget.field_name
                
                # التحقق من وجود قيمة للحقل في البيانات المرسلة
                if field_name in field_data:
                    # ملء الحقل بالقيمة (حتى لو كان مخفي)
                    widget.field_value = str(field_data[field_name])

                    # التأكد من أن الحقل يبقى مخفي بعد الملء
                    widget.field_flags = fitz.PDF_FIELD_IS_HIDDEN

                    widget.update()
                    filled_fields += 1
        
        # حفظ الملف مع البيانات الجديدة
        doc.save(output_pdf, garbage=4, deflate=True)
        doc.close()
        
        return {
            'success': True,
            'message': 'تم ملء الحقول بنجاح',
            'total_fields': total_fields,
            'filled_fields': filled_fields,
            'output_file': output_pdf
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'خطأ في ملء الحقول: {str(e)}',
            'total_fields': 0,
            'filled_fields': 0
        }


def get_pdf_field_names(pdf_file):
    """
    الحصول على أسماء جميع الحقول في ملف PDF
    
    Args:
        pdf_file: مسار ملف PDF
        
    Returns:
        list: قائمة بأسماء الحقول
    """
    
    try:
        doc = fitz.open(pdf_file)
        field_names = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            widgets = page.widgets()
            
            for widget in widgets:
                if widget.field_name not in field_names:
                    field_names.append(widget.field_name)
        
        doc.close()
        return field_names
        
    except Exception as e:
        print(f"خطأ في قراءة الحقول: {str(e)}")
        return []


def create_sample_data():
    """
    إنشاء بيانات تجريبية لملء الحقول
    
    Returns:
        dict: بيانات تجريبية
    """
    
    sample_data = {
        "serial_number": "12345",
        "date": "2024-01-15",
        "location_1": "الرياض",
        "name_1": "أحمد محمد علي",
        "location_2": "جدة", 
        "name_2": "فاطمة أحمد",
        "phone_1": "0501234567",
        "id_1": "1234567890",
        "location_3": "الدمام",
        "name_3": "محمد سعد",
        "phone_2": "0509876543",
        "id_2": "0987654321",
        "sin_num": "SIN123456",
        "car_prop": "تويوتا كامري",
        "car_city": "الرياض",
        "car_num": "أ ب ج 1234",
        "car_colar": "أبيض",
        "car_model": "2023",
        "car_type": "سيدان",
        "sasi_num": "CHASSIS123456",
        "badal_writing": "خمسة آلاف ريال",
        "badal_num": "5000",
        "mony_writing": "عشرة آلاف ريال",
        "mony_num": "10000",
        "mony_not_delevired_writing": "ألف ريال",
        "mo": "1000",
        "note_a": "ملاحظة أ",
        "note_b": "ملاحظة ب",
        "day": "الاثنين",
        "date_1": "2024-01-16",
        "t": "ت",
        "t_1": "ت1",
        "t_11": "ت11",
        "t_": "ت_",
        "sl": "سل",
        "bo": "بو",
        "name_33": "اسم 33",
        "name_22": "اسم 22",
        "qr": "QR123456"
    }
    
    return sample_data


# --- مثال على الاستخدام ---
if __name__ == "__main__":
    
    # 1. عرض أسماء الحقول الموجودة في الملف
    print("🔍 البحث عن الحقول في الملف...")
    field_names = get_pdf_field_names("pdf_with_fields.pdf")
    
    if field_names:
        print(f"📋 تم العثور على {len(field_names)} حقل:")
        for i, name in enumerate(field_names, 1):
            print(f"   {i}. {name}")
    else:
        print("❌ لم يتم العثور على أي حقول")
        exit()
    
    print("\n" + "="*50)
    
    # 2. إنشاء بيانات تجريبية
    print("📝 إنشاء بيانات تجريبية...")
    sample_data = create_sample_data()
    
    # عرض البيانات التي سيتم ملؤها
    print("📊 البيانات التي سيتم ملؤها:")
    for field, value in sample_data.items():
        if field in field_names:  # عرض فقط الحقول الموجودة
            print(f"   {field}: {value}")
    
    print("\n" + "="*50)
    
    # 3. ملء الحقول
    print("⚡ ملء الحقول...")
    result = fill_pdf_fields(
        input_pdf="pdf_with_fields.pdf",
        output_pdf="filled_document.pdf",
        field_data=sample_data
    )
    
    # 4. عرض النتائج
    if result['success']:
        print(f"✅ {result['message']}")
        print(f"📄 الملف الناتج: {result['output_file']}")
        print(f"📊 إجمالي الحقول: {result['total_fields']}")
        print(f"✏️ الحقول المملوءة: {result['filled_fields']}")
        
        if result['filled_fields'] < result['total_fields']:
            print(f"⚠️ لم يتم ملء {result['total_fields'] - result['filled_fields']} حقل")
            
    else:
        print(f"❌ {result['message']}")


# --- دالة لملء حقول محددة فقط ---
def fill_specific_fields():
    """مثال لملء حقول محددة فقط"""
    
    # بيانات محددة فقط
    specific_data = {
        "name_1": "سارة أحمد",
        "date": "2024-01-20",
        "location_1": "الرياض",
        "phone_1": "0501111111"
    }
    
    result = fill_pdf_fields(
        input_pdf="pdf_with_fields.pdf",
        output_pdf="partially_filled.pdf",
        field_data=specific_data
    )
    
    return result


# --- دالة لملء الحقول من ملف JSON ---
def fill_from_json(json_file):
    """ملء الحقول من ملف JSON"""
    
    import json
    
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        result = fill_pdf_fields(
            input_pdf="pdf_with_fields.pdf",
            output_pdf="filled_from_json.pdf",
            field_data=data
        )
        
        return result
        
    except Exception as e:
        return {
            'success': False,
            'message': f'خطأ في قراءة ملف JSON: {str(e)}'
        }


# تشغيل الأمثلة الإضافية (اختياري)
# specific_result = fill_specific_fields()
# json_result = fill_from_json("data.json")
