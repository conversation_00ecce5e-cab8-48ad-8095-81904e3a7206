* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, Helvetica, sans-serif;
}

.navigation {
    display: flex;
    position: fixed;
    align-items: center;
    justify-content: space-between;
    padding: 2rem 5%;
    top: 0;
    width: 100%;
    background: rgb(114, 0, 0);
    backdrop-filter: blur(10px);
    z-index: 25;
}

.logo {
    color:aliceblue;
    font-size: 2rem;
    font-weight: bold;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    color: aliceblue;
    text-decoration: none;
    transition: 0.3s;
}

.nav-links a:hover {
    color: black;
    
}

.hero {
    height: 100vh;
    background: black;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    padding: 0 5%;
}

.hero-content {
    max-width: 600px;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    color: aliceblue;
}

.hero p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: aliceblue;
}

.cta-button {
    background: rgb(114, 0, 0);
    color:aliceblue;
    padding: 1rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: 0.3s;
}

.cta-button:hover {
    transform: scale(1.05);
}