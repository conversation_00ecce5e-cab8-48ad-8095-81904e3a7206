import fitz  # PyMuPDF

def create_fillable_pdf_form(input_pdf, output_pdf, placeholders, 
                           field_style=None, show_labels=True):
    """
    دالة مبسطة لتحويل placeholders إلى حقول نموذج قابلة للتعبئة
    
    Args:
        input_pdf: مسار ملف PDF الأصلي
        output_pdf: مسار ملف PDF الناتج
        placeholders: قائمة أسماء الـ placeholders
        field_style: إعدادات مظهر الحقول (اختياري)
        show_labels: إظهار تسميات الحقول
    
    Returns:
        dict: معلومات عن نتيجة العملية
    """
    
    # الإعدادات الافتراضية للحقول
    default_style = {
        'font_size': 10,
        'border_color': (0, 0, 1),      # أزرق
        'fill_color': (0.98, 0.98, 1),  # خلفية زرقاء فاتحة جداً
        'text_color': (0, 0, 0),        # أسود
        'border_width': 1,
        'font_name': 'helv',
        'min_width': 80,
        'min_height': 16
    }
    
    # دمج الإعدادات المخصصة مع الافتراضية
    if field_style:
        default_style.update(field_style)
    
    try:
        # فتح ملف PDF
        doc = fitz.open(input_pdf)
        total_fields = 0
        
        # معالجة كل صفحة
        for page_num in range(len(doc)):
            page = doc[page_num]
            fields_on_page = []
            
            # البحث عن placeholders
            for name in placeholders:
                search_text = f"{{{{{name}}}}}"  # تنسيق {{placeholder}}
                results = page.search_for(search_text)
                
                for rect in results:
                    # حساب أبعاد الحقل
                    field_width = max(rect.width + 20, default_style['min_width'])
                    field_height = max(rect.height + 4, default_style['min_height'])
                    
                    # إنشاء مستطيل الحقل
                    field_rect = fitz.Rect(
                        rect.x0 - 2,
                        rect.y0 - 2,
                        rect.x0 + field_width,
                        rect.y0 + field_height
                    )
                    
                    fields_on_page.append({
                        'name': name,
                        'original_rect': rect,
                        'field_rect': field_rect
                    })
            
            # إزالة النصوص الأصلية
            for field in fields_on_page:
                page.add_redact_annot(field['original_rect'], fill=(1, 1, 1))
            page.apply_redactions()
            
            # إنشاء حقول النموذج
            for field in fields_on_page:
                # إنشاء حقل النص
                widget = fitz.Widget()
                widget.field_name = field['name']
                widget.field_type = fitz.PDF_WIDGET_TYPE_TEXT
                widget.rect = field['field_rect']
                widget.field_value = ""
                
                # تطبيق التنسيق
                widget.border_color = default_style['border_color']
                widget.fill_color = default_style['fill_color']
                widget.text_color = default_style['text_color']
                widget.text_font = default_style['font_name']
                widget.text_fontsize = default_style['font_size']
                widget.border_width = default_style['border_width']
                
                # إضافة الحقل إلى الصفحة
                page.add_widget(widget)
                total_fields += 1
                
                # إضافة تسمية الحقل (اختياري)
                if show_labels:
                    label_y = field['field_rect'].y0 - 10
                    if label_y > 0:
                        page.insert_text(
                            (field['field_rect'].x0, label_y),
                            field['name'],
                            fontsize=8,
                            color=(0.6, 0.6, 0.6)
                        )
        
        # حفظ الملف
        doc.save(output_pdf, garbage=4, deflate=True)
        doc.close()
        
        return {
            'success': True,
            'message': 'تم إنشاء النموذج بنجاح',
            'fields_created': total_fields,
            'output_file': output_pdf
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'خطأ: {str(e)}',
            'fields_created': 0
        }


# --- الاستخدام المباشر ---
if __name__ == "__main__":
    # قائمة الـ placeholders
    placeholders = [
        "serial_number", "t_11", "t_", "date", "location_1", "name_1",
        "location_2", "name_2", "phone_1", "id_1", "location_3", "name_3",
        "phone_2", "id_2", "sin_num", "car_prop", "car_city", "car_num",
        "car_colar", "car_model", "car_type", "sasi_num", "badal_writing", "badal_num",
        "mony_writing", "mony_num", "mony_not_delevired_writing", "mo",
        "note_a", "note_b", "day", "date_1", "t", "t_1",
        "sl", "bo", "name_33", "name_22", "qr"
    ]
    
    # إعدادات مخصصة للحقول (اختياري)
    custom_style = {
        'font_size': 11,
        'border_color': (0, 0.6, 0),      # أخضر
        'fill_color': (0.95, 1, 0.95),    # خلفية خضراء فاتحة
        'border_width': 1.2,
        'min_width': 90
    }
    
    # إنشاء النموذج
    result = create_fillable_pdf_form(
        input_pdf="template.pdf",
        output_pdf="fillable_form.pdf",
        placeholders=placeholders,
        field_style=custom_style,
        show_labels=True
    )
    
    # طباعة النتيجة
    if result['success']:
        print(f"✅ {result['message']}")
        print(f"📄 الملف: {result['output_file']}")
        print(f"🔢 الحقول: {result['fields_created']}")
        print("💡 افتح الملف في أي قارئ PDF لتعبئة الحقول!")
    else:
        print(f"❌ {result['message']}")


# --- مثال آخر بإعدادات مختلفة ---
def create_professional_form():
    """مثال لإنشاء نموذج بمظهر مهني"""
    
    placeholders = ["name", "date", "signature", "amount", "description"]
    
    professional_style = {
        'font_size': 12,
        'border_color': (0.2, 0.2, 0.2),    # رمادي داكن
        'fill_color': (0.98, 0.98, 0.98),   # رمادي فاتح جداً
        'text_color': (0, 0, 0),            # أسود
        'border_width': 0.8,
        'min_width': 120,
        'min_height': 20
    }
    
    result = create_fillable_pdf_form(
        input_pdf="template.pdf",
        output_pdf="professional_form.pdf",
        placeholders=placeholders,
        field_style=professional_style,
        show_labels=False  # بدون تسميات للمظهر النظيف
    )
    
    return result


def create_colorful_form():
    """مثال لإنشاء نموذج ملون"""
    
    placeholders = ["student_name", "class", "grade", "teacher", "date"]
    
    colorful_style = {
        'font_size': 11,
        'border_color': (1, 0.5, 0),        # برتقالي
        'fill_color': (1, 0.95, 0.8),       # خلفية برتقالية فاتحة
        'text_color': (0.2, 0.2, 0.2),      # رمادي داكن
        'border_width': 2,
        'min_width': 100,
        'min_height': 18
    }
    
    result = create_fillable_pdf_form(
        input_pdf="template.pdf",
        output_pdf="colorful_form.pdf",
        placeholders=placeholders,
        field_style=colorful_style,
        show_labels=True
    )
    
    return result


# تشغيل الأمثلة الإضافية (اختياري)
# professional_result = create_professional_form()
# colorful_result = create_colorful_form()
