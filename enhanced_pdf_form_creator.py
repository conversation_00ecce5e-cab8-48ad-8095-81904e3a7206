import fitz  # PyMuPDF
import json
import os
from typing import List, Dict, Tuple, Optional

class PDFFormCreator:
    """
    فئة محسنة لإنشاء نماذج PDF قابلة للتعبئة من placeholders
    """
    
    def __init__(self, input_pdf: str, output_pdf: str = None):
        """
        تهيئة منشئ النماذج
        
        Args:
            input_pdf: مسار ملف PDF الأصلي
            output_pdf: مسار ملف PDF الناتج (اختياري)
        """
        self.input_pdf = input_pdf
        self.output_pdf = output_pdf or f"form_{os.path.basename(input_pdf)}"
        self.doc = None
        self.field_settings = {
            'font_size': 10,
            'border_color': (0, 0, 1),      # أزرق
            'fill_color': (0.95, 0.95, 1),  # خلفية زرقاء فاتحة
            'text_color': (0, 0, 0),        # أسود
            'border_width': 1,
            'font_name': 'helv',            # Helvetica
            'min_width': 80,
            'min_height': 16,
            'padding': 4
        }
        
    def set_field_style(self, **kwargs):
        """
        تخصيص مظهر الحقول
        
        Args:
            font_size: حجم الخط
            border_color: لون الحدود (R, G, B)
            fill_color: لون الخلفية (R, G, B)
            text_color: لون النص (R, G, B)
            border_width: عرض الحدود
            font_name: اسم الخط
            min_width: العرض الأدنى للحقل
            min_height: الارتفاع الأدنى للحقل
            padding: المسافة الإضافية حول النص
        """
        self.field_settings.update(kwargs)
    
    def find_placeholders(self, placeholders: List[str], 
                         placeholder_format: str = "{{{}}}") -> Dict[int, List[Dict]]:
        """
        البحث عن placeholders في جميع صفحات PDF
        
        Args:
            placeholders: قائمة أسماء الـ placeholders
            placeholder_format: تنسيق الـ placeholder (افتراضي: {{}})
            
        Returns:
            قاموس يحتوي على معلومات الحقول لكل صفحة
        """
        found_fields = {}
        
        for page_num in range(len(self.doc)):
            page = self.doc[page_num]
            page_fields = []
            
            for name in placeholders:
                search_text = placeholder_format.format(name)
                results = page.search_for(search_text)
                
                for rect in results:
                    # حساب أبعاد الحقل
                    field_width = max(rect.width + self.field_settings['padding'] * 2, 
                                    self.field_settings['min_width'])
                    field_height = max(rect.height + self.field_settings['padding'], 
                                     self.field_settings['min_height'])
                    
                    # إنشاء مستطيل الحقل
                    field_rect = fitz.Rect(
                        rect.x0 - 2,
                        rect.y0 - 2,
                        rect.x0 + field_width,
                        rect.y0 + field_height
                    )
                    
                    page_fields.append({
                        'name': name,
                        'original_rect': rect,
                        'field_rect': field_rect,
                        'placeholder_text': search_text,
                        'field_type': 'text'  # يمكن تخصيصه لاحقاً
                    })
            
            if page_fields:
                found_fields[page_num] = page_fields
                
        return found_fields
    
    def create_form_fields(self, found_fields: Dict[int, List[Dict]], 
                          add_labels: bool = True, 
                          label_position: str = 'top') -> int:
        """
        إنشاء حقول النموذج الفعلية
        
        Args:
            found_fields: معلومات الحقول المكتشفة
            add_labels: إضافة تسميات للحقول
            label_position: موضع التسمية ('top', 'bottom', 'left', 'right')
            
        Returns:
            عدد الحقول المنشأة
        """
        total_fields = 0
        
        for page_num, fields in found_fields.items():
            page = self.doc[page_num]
            
            # إزالة النصوص الأصلية أولاً
            for field_info in fields:
                page.add_redact_annot(field_info['original_rect'], fill=(1, 1, 1))
            page.apply_redactions()
            
            # إنشاء الحقول
            for field_info in fields:
                self._create_single_field(page, field_info, add_labels, label_position)
                total_fields += 1
                
        return total_fields
    
    def _create_single_field(self, page, field_info: Dict, 
                           add_labels: bool, label_position: str):
        """
        إنشاء حقل واحد
        """
        field_name = field_info['name']
        field_rect = field_info['field_rect']
        
        # إنشاء حقل النموذج
        widget = fitz.Widget()
        widget.field_name = field_name
        widget.field_type = fitz.PDF_WIDGET_TYPE_TEXT
        widget.rect = field_rect
        widget.field_value = ""
        
        # تطبيق الإعدادات
        widget.border_color = self.field_settings['border_color']
        widget.fill_color = self.field_settings['fill_color']
        widget.text_color = self.field_settings['text_color']
        widget.text_font = self.field_settings['font_name']
        widget.text_fontsize = self.field_settings['font_size']
        widget.border_width = self.field_settings['border_width']
        
        # إضافة الحقل
        page.add_widget(widget)
        
        # إضافة التسمية إذا كانت مطلوبة
        if add_labels:
            self._add_field_label(page, field_name, field_rect, label_position)
    
    def _add_field_label(self, page, field_name: str, field_rect: fitz.Rect, 
                        position: str):
        """
        إضافة تسمية للحقل
        """
        label_font_size = max(6, self.field_settings['font_size'] - 2)
        label_color = (0.5, 0.5, 0.5)
        
        if position == 'top':
            label_y = field_rect.y0 - 12
            label_x = field_rect.x0
            if label_y > 0:
                page.insert_text((label_x, label_y), field_name, 
                               fontsize=label_font_size, color=label_color)
        
        elif position == 'bottom':
            label_y = field_rect.y1 + 12
            label_x = field_rect.x0
            if label_y < page.rect.height:
                page.insert_text((label_x, label_y), field_name, 
                               fontsize=label_font_size, color=label_color)
        
        elif position == 'left':
            label_y = field_rect.y0 + (field_rect.height / 2) + 3
            label_x = field_rect.x0 - 60
            if label_x > 0:
                page.insert_text((label_x, label_y), field_name, 
                               fontsize=label_font_size, color=label_color)
        
        elif position == 'right':
            label_y = field_rect.y0 + (field_rect.height / 2) + 3
            label_x = field_rect.x1 + 5
            if label_x < page.rect.width:
                page.insert_text((label_x, label_y), field_name, 
                               fontsize=label_font_size, color=label_color)
    
    def create_form(self, placeholders: List[str], 
                   placeholder_format: str = "{{{}}}",
                   add_labels: bool = True,
                   label_position: str = 'top') -> Dict[str, any]:
        """
        إنشاء النموذج الكامل
        
        Args:
            placeholders: قائمة أسماء الـ placeholders
            placeholder_format: تنسيق الـ placeholder
            add_labels: إضافة تسميات للحقول
            label_position: موضع التسميات
            
        Returns:
            معلومات عن العملية
        """
        try:
            # فتح الملف
            self.doc = fitz.open(self.input_pdf)
            
            # البحث عن placeholders
            found_fields = self.find_placeholders(placeholders, placeholder_format)
            
            if not found_fields:
                return {
                    'success': False,
                    'message': 'لم يتم العثور على أي placeholders',
                    'fields_created': 0
                }
            
            # إنشاء الحقول
            fields_created = self.create_form_fields(found_fields, add_labels, label_position)
            
            # حفظ الملف
            self.doc.save(self.output_pdf, garbage=4, deflate=True)
            self.doc.close()
            
            return {
                'success': True,
                'message': f'تم إنشاء النموذج بنجاح',
                'fields_created': fields_created,
                'output_file': self.output_pdf,
                'pages_processed': len(found_fields)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'خطأ في إنشاء النموذج: {str(e)}',
                'fields_created': 0
            }
    
    def save_field_info(self, filename: str = None):
        """
        حفظ معلومات الحقول في ملف JSON
        """
        if not filename:
            filename = f"fields_info_{os.path.splitext(os.path.basename(self.output_pdf))[0]}.json"
        
        # هنا يمكن إضافة كود لحفظ معلومات الحقول
        pass


# --- مثال على الاستخدام ---
if __name__ == "__main__":
    # قائمة الـ placeholders
    placeholders = [
        "serial_number", "t_11", "t_", "date", "location_1", "name_1",
        "location_2", "name_2", "phone_1", "id_1", "location_3", "name_3",
        "phone_2", "id_2", "sin_num", "car_prop", "car_city", "car_num",
        "car_colar", "car_model", "car_type", "sasi_num", "badal_writing", "badal_num",
        "mony_writing", "mony_num", "mony_not_delevired_writing", "mo",
        "note_a", "note_b", "day", "date_1", "t", "t_1",
        "sl", "bo", "name_33", "name_22", "qr"
    ]
    
    # إنشاء منشئ النماذج
    form_creator = PDFFormCreator("template.pdf", "enhanced_form.pdf")
    
    # تخصيص مظهر الحقول
    form_creator.set_field_style(
        font_size=11,
        border_color=(0, 0.5, 0),      # أخضر
        fill_color=(0.95, 1, 0.95),    # خلفية خضراء فاتحة
        border_width=1.5,
        min_width=100,
        padding=6
    )
    
    # إنشاء النموذج
    result = form_creator.create_form(
        placeholders=placeholders,
        add_labels=True,
        label_position='top'
    )
    
    # طباعة النتائج
    if result['success']:
        print(f"✅ {result['message']}")
        print(f"📄 الملف الناتج: {result['output_file']}")
        print(f"🔢 عدد الحقول المنشأة: {result['fields_created']}")
        print(f"📖 عدد الصفحات المعالجة: {result['pages_processed']}")
        print("💡 يمكنك الآن فتح الملف وتعبئة الحقول مباشرة!")
    else:
        print(f"❌ {result['message']}")
