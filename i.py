import fitz  # PyMuPDF

# --- إعدادات ---
input_pdf = "template.pdf"               # اسم ملف PDF الأصلي
output_pdf = "pdf_with_fields.pdf"       # اسم الملف الناتج

# قائمة الـ placeholders التي تريد تحويلها إلى حقول نموذج
placeholders = [
    "serial_number", "t_11", "t_", "date", "location_1", "name_1",
    "location_2", "name_2", "phone_1", "id_1", "location_3", "name_3",
    "phone_2", "id_2", "sin_num", "car_prop", "car_city", "car_num",
    "car_colar", "car_model", "car_type", "sasi_num", "badal_writing", "badal_num",
    "mony_writing", "mony_num", "mony_not_delevired_writing", "mo",
    "note_a", "note_b", "day", "date_1", "t", "t_1",
    "sl", "bo", "name_33", "name_22", "qr"
]

# --- فتح ملف PDF ---
doc = fitz.open(input_pdf)

# --- إعدادات الحقول ---
font_size = 10
field_border_color = (0, 0, 1)  # أزرق للحدود
field_bg_color = (0.95, 0.95, 1)  # خلفية زرقاء فاتحة
text_color = (0, 0, 0)   # أسود للنص

# --- معالجة كل صفحة ---
for page_num in range(len(doc)):
    page = doc[page_num]

    # قائمة لحفظ معلومات الحقول المكتشفة
    found_fields = []

    # البحث عن placeholders وجمع معلوماتها
    for name in placeholders:
        search_text = f"{{{{{name}}}}}"  # شكل النص المطلوب مثلاً {{name_1}}
        results = page.search_for(search_text)

        for rect in results:
            # حساب أبعاد الحقل بناءً على النص الأصلي
            text_width = rect.width
            text_height = rect.height

            # تحديد حجم الحقل (أكبر قليلاً من النص الأصلي)
            field_width = max(text_width + 20, 80)  # حد أدنى 80 نقطة
            field_height = max(text_height + 4, 16)  # حد أدنى 16 نقطة

            # إنشاء مستطيل الحقل
            field_rect = fitz.Rect(
                rect.x0 - 2,  # بداية أكثر قليلاً لليسار
                rect.y0 - 2,  # بداية أكثر قليلاً للأعلى
                rect.x0 + field_width,
                rect.y0 + field_height
            )

            found_fields.append({
                'name': name,
                'original_rect': rect,
                'field_rect': field_rect,
                'placeholder_text': search_text
            })

    # إزالة النصوص الأصلية (placeholders)
    for field_info in found_fields:
        page.add_redact_annot(field_info['original_rect'], fill=(1, 1, 1))
    page.apply_redactions()

    # إنشاء حقول النموذج الفعلية
    for field_info in found_fields:
        field_name = field_info['name']
        field_rect = field_info['field_rect']

        # إنشاء حقل نص قابل للتعبئة
        widget = fitz.Widget()
        widget.field_name = field_name
        widget.field_type = fitz.PDF_WIDGET_TYPE_TEXT
        widget.rect = field_rect
        widget.field_value = ""  # قيمة فارغة في البداية
        widget.field_flags = fitz.PDF_FIELD_IS_REQUIRED  # جعل الحقل مطلوب (اختياري)

        # تنسيق الحقل
        widget.border_color = field_border_color
        widget.fill_color = field_bg_color
        widget.text_color = text_color
        widget.text_font = "helv"  # خط Helvetica
        widget.text_fontsize = font_size
        widget.border_width = 1

        # إضافة الحقل إلى الصفحة
        page.add_widget(widget)

        # إضافة تسمية صغيرة للحقل (اختياري)
        label_y = field_rect.y0 - 12
        if label_y > 0:  # التأكد من أن التسمية لن تخرج من الصفحة
            page.insert_text(
                (field_rect.x0, label_y),
                f"{field_name}",
                fontsize=8,
                color=(0.5, 0.5, 0.5)  # رمادي فاتح
            )

# --- حفظ الملف الناتج ---
doc.save(output_pdf, garbage=4, deflate=True)
doc.close()
print(f"✅ تم إنشاء PDF مع حقول نموذج قابلة للتعبئة: {output_pdf}")
print(f"📝 تم إنشاء {len(placeholders)} حقل نموذج")
print("💡 يمكنك الآن فتح الملف في أي قارئ PDF وتعبئة الحقول مباشرة")
